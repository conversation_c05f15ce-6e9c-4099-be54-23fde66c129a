package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/guonaihong/gout"
	"github.com/volcengine/volc-sdk-golang/base"
)

type DefaultVolcesClient struct {
	Config Config
}

func (c *DefaultVolcesClient) WriteDataset(documents []Document) (*http.Response, error) {
	body, _ := json.Marshal(map[string]interface{}{"documents": documents})
	endpoint := fmt.Sprintf("/dataset/%s/write", c.Config.DatasetID)
	return c.makeRequest(endpoint, body)
}

func (c *DefaultVolcesClient) DeleteDataset(documentIDs []string) (*http.Response, error) {
	body, _ := json.Marshal(documentIDs)
	endpoint := fmt.Sprintf("/dataset/%s/delete", c.Config.DatasetID)
	return c.makeRequest(endpoint, body)
}

func (c *DefaultVolcesClient) SearchDataset(query string, limit int) (*http.Response, error) {
	body, _ := json.Marshal(map[string]interface{}{"query": query, "limit": limit})
	endpoint := fmt.Sprintf("/application/%s/search", c.Config.ApplicationID)
	return c.makeRequest(endpoint, body)
}

func (c *DefaultVolcesClient) makeRequest(endpoint string, body []byte) (*http.Response, error) {
	// Use gout to make the HTTP request
	return gout.
		POST(fmt.Sprintf("%s%s", c.Config.BaseURL, endpoint)).
		SetHeader(gout.H{
			"Accept":       "application/json",
			"Content-Type": "application/json",
		}).
		SetJSON(body).
		RequestUse(
			&VolcesRequestMiddler{c.Config},
		).
		Debug(true).
		Response()

	//Do()
	//
	//return nil, err
}

type SearchResponse struct {
	RequestId string `json:"request_id"`
	Result    struct {
		SearchResults []struct {
			Id            string `json:"_id"`
			DisplayFields struct {
				ItemId   string `json:"item_id"`
				Title    string `json:"title"`
				Category string `json:"category"`
				Status   int    `json:"status"`
				Images   []struct {
					ImageUrl string `json:"image_url"`
				} `json:"images"`
			} `json:"display_fields"`
		} `json:"search_results"`
		TotalItems int `json:"total_items"`
	} `json:"result"`
}
type VolcesRequestMiddler struct {
	Config Config
}

func (d *VolcesRequestMiddler) ModifyRequest(req *http.Request) error {
	credential := base.Credentials{
		AccessKeyID:     d.Config.AccessKey,
		SecretAccessKey: d.Config.SecretKey,
		Service:         "aisearch",
		Region:          "cn-north-1",
	}
	req = credential.Sign(req)
	return nil
}

func (c *DefaultVolcesClient) PrepareRequest(method string, endpoint string, query url.Values, body []byte) *http.Request {
	u, err := url.Parse(endpoint)
	if err != nil {
		panic(err)
	}
	// // u := url.URL{
	// 	Scheme: Schema,
	// 	Host:   Host,
	// 	Path:   endpoint,
	// }
	if query != nil {
		u.RawQuery = query.Encode()
	}
	req, _ := http.NewRequest(strings.ToUpper(method), u.String(), bytes.NewReader(body))
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Host", u.Host)
	credential := base.Credentials{
		AccessKeyID:     c.Config.AccessKey,
		SecretAccessKey: c.Config.SecretKey,
		Service:         "aisearch",
		Region:          "cn-north-1",
	}
	req = credential.Sign(req)
	return req
}

// func main() {
// 	path := fmt.Sprintf("/api/v1/application/%s/search", "实际应用id")
// 	urlParam := url.Values{}
// 	bodyData := map[string]interface{}{
// 		"query": map[string]string{
// 			"text": "测试",
// 		},
// 		"dataset_id":  "实际数据集id",
// 		"page_number": 1,
// 		"page_size":   10,
// 	}
// 	body, _ := json.Marshal(bodyData)
// 	req := PrepareRequest("POST", path, AK, SK, urlParam, body)

// 	// send request
// 	rsp, err := http.DefaultClient.Do(req)
// 	if err != nil {
// 		fmt.Println("读取响应内容出错:", err)
// 		return
// 	}
// 	defer rsp.Body.Close()

// 	respBody, err := io.ReadAll(rsp.Body)
// 	if err != nil {
// 		fmt.Println("读取响应内容出错:", err)
// 		return
// 	}
// 	fmt.Println("响应状态码:", rsp.StatusCode)
// 	fmt.Println("响应内容:", string(respBody))
// }
