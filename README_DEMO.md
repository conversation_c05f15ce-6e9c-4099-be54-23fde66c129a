# 火山AI搜索 Demo

这是一个基于Golang后端和JavaScript前端的火山AI搜索演示应用。

## 功能特性

- 🔐 **安全鉴权**: 支持ak/sk鉴权机制
- 📝 **数据写入**: 支持文档数据的新增和更新
- 🗑️ **数据删除**: 支持批量删除指定文档
- 🔍 **智能搜索**: 基于火山AI的智能搜索功能
- 🎨 **现代界面**: 响应式设计，支持移动端

## 项目结构

```
ai-search/
├── main.go          # Golang后端服务
├── go.mod           # Go模块依赖
├── config.json      # 配置文件
├── index.html       # 前端页面
└── README_DEMO.md   # 使用说明
```

## 快速开始

### 1. 配置设置

编辑 `config.json` 文件，填入您的火山AI搜索凭证：

```json
{
  "access_key": "your_access_key_here",
  "secret_key": "your_secret_key_here",
  "dataset_id": "your_dataset_id_here",
  "application_id": "your_application_id_here",
  "base_url": "https://aisearch.cn-beijing.volces.com/api/v1"
}
```

### 2. 安装依赖

```bash
go mod tidy
```

### 3. 启动服务

```bash
go run main.go
```

服务将在 `http://localhost:8080` 启动。

### 4. 访问Demo

在浏览器中打开 `http://localhost:8080` 即可使用Demo界面。

## API接口

### 数据写入/更新
- **URL**: `POST /api/write`
- **功能**: 向数据集中写入或更新文档
- **请求体**:
```json
{
  "documents": [
    {
      "id": "doc1",
      "content": {
        "title": "示例标题",
        "text": "这是示例内容"
      }
    }
  ]
}
```

### 数据删除
- **URL**: `POST /api/delete`
- **功能**: 从数据集中删除指定文档
- **请求体**:
```json
{
  "document_ids": ["doc1", "doc2"]
}
```

### 数据搜索
- **URL**: `POST /api/search`
- **功能**: 在数据集中进行智能搜索
- **请求体**:
```json
{
  "query": "搜索关键词",
  "limit": 10
}
```

## 技术栈

- **后端**: Golang + Gin框架
- **前端**: 原生JavaScript + HTML5 + CSS3
- **HTTP客户端**: 原生fetch API
- **鉴权**: HMAC-SHA256签名算法（可自定义）
- **架构**: 接口驱动设计，支持自定义鉴权实现

## 自定义鉴权

代码已重构为接口驱动架构，您可以轻松自定义鉴权方法：

### 快速自定义
1. 查看 `custom_auth_example.go` 了解如何创建自定义客户端
2. 阅读 `CUSTOM_AUTH_GUIDE.md` 获取详细指南
3. 在 `main.go` 的 `main()` 函数中替换客户端实现：

```go
// 默认实现
volcesClient = &DefaultVolcesClient{Config: config}

// 自定义实现
volcesClient = &CustomVolcesClient{Config: config}
```

### 核心接口
```go
type VolcesClient interface {
    WriteDataset(documents []Document) (*http.Response, error)
    DeleteDataset(documentIDs []string) (*http.Response, error)
    SearchDataset(query string, limit int) (*http.Response, error)
}
```

## 注意事项

1. 确保您的火山AI搜索账户有足够的权限
2. 请妥善保管您的access_key和secret_key
3. 数据集ID和应用ID需要在火山AI控制台中获取
4. 建议在生产环境中使用HTTPS

## 故障排除

- 如果遇到CORS错误，请检查后端CORS配置
- 如果鉴权失败，请验证ak/sk和时间戳是否正确
- 如果API调用失败，请检查网络连接和配置参数

## 开发说明

- 后端使用Gin框架提供RESTful API
- 前端使用现代CSS Grid布局，支持响应式设计
- 鉴权机制严格按照火山AI官方文档实现
- 所有API调用都包含错误处理和用户友好的反馈
