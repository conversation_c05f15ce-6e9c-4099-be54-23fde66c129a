package app

// Document 文档结构
type Document struct {
	ID      string                 `json:"id"`
	Content map[string]interface{} `json:"content"`
}

// WriteRequest 写入请求结构
type WriteRequest struct {
	Documents []Document `json:"documents"`
}

// DeleteRequest 删除请求结构
type DeleteRequest struct {
	DocumentIDs []string `json:"document_ids"`
}

type DocumentResponse struct {
	RequestId string `json:"request_id"`
}

// SearchRequest 搜索请求结构
type SearchRequest struct {
	Query string `json:"query"`
	Limit int    `json:"limit,omitempty"`
}

// ChatSearchRequest 对话式搜索请求结构
type ChatSearchRequest struct {
	Query       string            `json:"query"`
	Limit       int               `json:"limit,omitempty"`
	SessionID   string            `json:"session_id,omitempty"`
	Context     map[string]string `json:"context,omitempty"`
	Temperature float64           `json:"temperature,omitempty"`
}

// QueryCompletionRequest 搜索词补全请求结构
type QueryCompletionRequest struct {
	Query string `json:"query"`
	Limit int    `json:"limit,omitempty"`
}

// SearchResponse 搜索响应结构
type SearchResponse struct {
	RequestId string `json:"request_id"`
	Result    struct {
		SearchResults []struct {
			Id            string `json:"_id"`
			DisplayFields struct {
				Keywords   string `json:"keywords"`
				OldPhotoId int    `json:"old_photo_id"`
				Title      string `json:"title"`
				Url        string `json:"url"`
			} `json:"display_fields"`
			RecallInfo []struct {
				RecallRank   int     `json:"recall_rank"`
				RecallReason string  `json:"recall_reason"`
				RecallScore  float64 `json:"recall_score"`
			} `json:"recall_info"`
			Score float64 `json:"score"`
		} `json:"search_results"`
		TotalItems int `json:"total_items"`
	} `json:"result"`
}

// ChatSearchResponse 对话式搜索响应结构
type ChatSearchResponse struct {
	RequestId string `json:"request_id"`
	Result    struct {
		Answer        string `json:"answer"`
		SessionID     string `json:"session_id"`
		SearchResults []struct {
			Id            string `json:"_id"`
			DisplayFields struct {
				ItemId   string `json:"item_id"`
				Title    string `json:"title"`
				Category string `json:"category"`
				Status   int    `json:"status"`
				Images   []struct {
					ImageUrl string `json:"image_url"`
				} `json:"images"`
			} `json:"display_fields"`
			Score float64 `json:"score"`
		} `json:"search_results"`
		TotalItems int `json:"total_items"`
	} `json:"result"`
}

// QueryCompletionResponse 搜索词补全响应结构
type QueryCompletionResponse struct {
	RequestId string `json:"request_id"`
	Result    struct {
		Completions []struct {
			Text  string  `json:"text"`
			Score float64 `json:"score"`
		} `json:"completions"`
	} `json:"result"`
}

// VolcesClient 火山AI客户端接口
type VolcesClient interface {
	WriteDataset(documents []Document) (*DocumentResponse, error)
	DeleteDataset(documentIDs []string) (*DocumentResponse, error)
	SearchDataset(query string, limit int) (*SearchResponse, error)
	ChatSearchDataset(query string, limit int, sessionID string, context map[string]string, temperature float64) (*ChatSearchResponse, error)
	QueryCompletion(query string, limit int) (*QueryCompletionResponse, error)
}
