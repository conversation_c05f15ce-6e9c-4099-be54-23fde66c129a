package app

// Document 文档结构
type Document struct {
	ID      string                 `json:"id"`
	Content map[string]interface{} `json:"content"`
}

// WriteRequest 写入请求结构
type WriteRequest struct {
	Documents []Document `json:"documents"`
}

// DeleteRequest 删除请求结构
type DeleteRequest struct {
	DocumentIDs []string `json:"document_ids"`
}

type DocumentResponse struct {
	RequestId string `json:"request_id"`
}

// SearchRequest 搜索请求结构
type SearchRequest struct {
	Query string `json:"query"`
	Limit int    `json:"limit,omitempty"`
}

// SearchResponse 搜索响应结构
type SearchResponse struct {
	RequestId string `json:"request_id"`
	Result    struct {
		SearchResults []struct {
			Id            string `json:"_id"`
			DisplayFields struct {
				ItemId   string `json:"item_id"`
				Title    string `json:"title"`
				Category string `json:"category"`
				Status   int    `json:"status"`
				Images   []struct {
					ImageUrl string `json:"image_url"`
				} `json:"images"`
			} `json:"display_fields"`
		} `json:"search_results"`
		TotalItems int `json:"total_items"`
	} `json:"result"`
}

// VolcesClient 火山AI客户端接口
type VolcesClient interface {
	WriteDataset(documents []Document) (*DocumentResponse, error)
	DeleteDataset(documentIDs []string) (*DocumentResponse, error)
	SearchDataset(query string, limit int) (*SearchResponse, error)
}
