package config

import (
	"os"
	"path/filepath"
	"testing"
)

func TestLoadConfig(t *testing.T) {
	// 创建临时配置文件
	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "test_config.json")

	configContent := `{
		"access_key": "test_ak",
		"secret_key": "test_sk",
		"dataset_id": "test_dataset",
		"application_id": "test_app",
		"base_url": "https://test.example.com"
	}`

	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test config file: %v", err)
	}

	// 测试加载配置
	cfg, err := LoadConfig(configPath)
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// 验证配置内容
	if cfg.AccessKey != "test_ak" {
		t.<PERSON>("Expected AccessKey 'test_ak', got '%s'", cfg.<PERSON>Key)
	}
	if cfg.SecretKey != "test_sk" {
		t.<PERSON><PERSON>("Expected SecretKey 'test_sk', got '%s'", cfg.SecretKey)
	}
	if cfg.DatasetID != "test_dataset" {
		t.Errorf("Expected DatasetID 'test_dataset', got '%s'", cfg.DatasetID)
	}
	if cfg.ApplicationID != "test_app" {
		t.Errorf("Expected ApplicationID 'test_app', got '%s'", cfg.ApplicationID)
	}
	if cfg.BaseURL != "https://test.example.com" {
		t.Errorf("Expected BaseURL 'https://test.example.com', got '%s'", cfg.BaseURL)
	}
}

func TestConfigValidate(t *testing.T) {
	// 测试有效配置
	validConfig := &Config{
		AccessKey:     "test_ak",
		SecretKey:     "test_sk",
		DatasetID:     "test_dataset",
		ApplicationID: "test_app",
		BaseURL:       "https://test.example.com",
	}

	if err := validConfig.Validate(); err != nil {
		t.Errorf("Valid config should not return error: %v", err)
	}

	// 测试无效配置
	invalidConfigs := []*Config{
		{SecretKey: "sk", DatasetID: "ds", ApplicationID: "app", BaseURL: "url"},  // 缺少 AccessKey
		{AccessKey: "ak", DatasetID: "ds", ApplicationID: "app", BaseURL: "url"},  // 缺少 SecretKey
		{AccessKey: "ak", SecretKey: "sk", ApplicationID: "app", BaseURL: "url"},  // 缺少 DatasetID
		{AccessKey: "ak", SecretKey: "sk", DatasetID: "ds", BaseURL: "url"},       // 缺少 ApplicationID
		{AccessKey: "ak", SecretKey: "sk", DatasetID: "ds", ApplicationID: "app"}, // 缺少 BaseURL
	}

	for i, cfg := range invalidConfigs {
		if err := cfg.Validate(); err == nil {
			t.Errorf("Invalid config %d should return error", i)
		}
	}
}
