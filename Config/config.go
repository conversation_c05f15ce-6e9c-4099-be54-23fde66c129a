package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// Config 应用配置结构
type Config struct {
	AccessKey     string `json:"access_key"`
	SecretKey     string `json:"secret_key"`
	DatasetID     string `json:"dataset_id"`
	ApplicationID string `json:"application_id"`
	BaseURL       string `json:"base_url"`
}

// LoadConfig 从指定路径加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 如果没有指定路径，使用默认路径
	if configPath == "" {
		configPath = filepath.Join("Config", "config.json")
	}

	file, err := os.Open(configPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var config Config
	decoder := json.NewDecoder(file)
	err = decoder.Decode(&config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

// Validate 验证配置是否完整
func (c *Config) Validate() error {
	if c.<PERSON>ey == "" {
		return fmt.Errorf("access_key is required")
	}
	if c.<PERSON>ey == "" {
		return fmt.Errorf("secret_key is required")
	}
	if c.DatasetID == "" {
		return fmt.Errorf("dataset_id is required")
	}
	if c.ApplicationID == "" {
		return fmt.Errorf("application_id is required")
	}
	if c.BaseURL == "" {
		return fmt.Errorf("base_url is required")
	}
	return nil
}
