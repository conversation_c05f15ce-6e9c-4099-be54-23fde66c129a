<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>火山AI搜索 Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        input, textarea, button {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        input:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        textarea {
            height: 120px;
            resize: vertical;
            font-family: 'Courier New', monospace;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .response.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .response.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #667eea;
            font-weight: 600;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 火山AI搜索 Demo</h1>
            <p>数据集管理与智能搜索演示平台</p>
        </div>
        
        <div class="content">
            <div class="grid">
                <!-- 数据写入 -->
                <div class="section">
                    <h2>📝 数据写入/更新</h2>
                    <div class="form-group">
                        <label for="writeData">文档数据 (JSON格式):</label>
                        <textarea id="writeData" placeholder='[
  {
    "id": "doc1",
    "content": {
      "title": "示例标题",
      "text": "这是示例内容"
    }
  }
]'></textarea>
                    </div>
                    <button onclick="writeDataset()">写入数据</button>
                    <div class="loading" id="writeLoading">正在写入数据...</div>
                    <div id="writeResponse" class="response" style="display: none;"></div>
                </div>
                
                <!-- 数据删除 -->
                <div class="section">
                    <h2>🗑️ 数据删除</h2>
                    <div class="form-group">
                        <label for="deleteIds">文档ID列表 (JSON数组):</label>
                        <textarea id="deleteIds" placeholder='["doc1", "doc2"]'></textarea>
                    </div>
                    <button onclick="deleteDataset()">删除数据</button>
                    <div class="loading" id="deleteLoading">正在删除数据...</div>
                    <div id="deleteResponse" class="response" style="display: none;"></div>
                </div>
                
                <!-- 数据搜索 -->
                <div class="section">
                    <h2>🔍 智能搜索</h2>
                    <div class="form-group">
                        <label for="searchQuery">搜索查询:</label>
                        <input type="text" id="searchQuery" placeholder="输入搜索关键词">
                    </div>
                    <div class="form-group">
                        <label for="searchLimit">返回结果数量:</label>
                        <input type="number" id="searchLimit" value="10" min="1" max="100">
                    </div>
                    <button onclick="searchDataset()">开始搜索</button>
                    <div class="loading" id="searchLoading">正在搜索...</div>
                    <div id="searchResponse" class="response" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function makeRequest(url, data, loadingId, responseId) {
            const loading = document.getElementById(loadingId);
            const response = document.getElementById(responseId);
            
            loading.style.display = 'block';
            response.style.display = 'none';
            
            try {
                const res = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await res.text();
                
                loading.style.display = 'none';
                response.style.display = 'block';
                response.className = `response ${res.ok ? 'success' : 'error'}`;
                response.textContent = result;
                
            } catch (error) {
                loading.style.display = 'none';
                response.style.display = 'block';
                response.className = 'response error';
                response.textContent = `请求失败: ${error.message}`;
            }
        }
        
        async function writeDataset() {
            const data = document.getElementById('writeData').value;
            try {
                const documents = JSON.parse(data);
                await makeRequest('/api/write', { documents }, 'writeLoading', 'writeResponse');
            } catch (error) {
                document.getElementById('writeResponse').style.display = 'block';
                document.getElementById('writeResponse').className = 'response error';
                document.getElementById('writeResponse').textContent = `JSON格式错误: ${error.message}`;
            }
        }
        
        async function deleteDataset() {
            const data = document.getElementById('deleteIds').value;
            try {
                const documentIds = JSON.parse(data);
                await makeRequest('/api/delete', { document_ids: documentIds }, 'deleteLoading', 'deleteResponse');
            } catch (error) {
                document.getElementById('deleteResponse').style.display = 'block';
                document.getElementById('deleteResponse').className = 'response error';
                document.getElementById('deleteResponse').textContent = `JSON格式错误: ${error.message}`;
            }
        }
        
        async function searchDataset() {
            const query = document.getElementById('searchQuery').value;
            const limit = parseInt(document.getElementById('searchLimit').value);
            
            if (!query.trim()) {
                document.getElementById('searchResponse').style.display = 'block';
                document.getElementById('searchResponse').className = 'response error';
                document.getElementById('searchResponse').textContent = '请输入搜索查询';
                return;
            }
            
            await makeRequest('/api/search', { query, limit }, 'searchLoading', 'searchResponse');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('火山AI搜索 Demo 已加载');
        });
    </script>
</body>
</html>
