package app

import (
	"net/http"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// SetupRouter 设置路由
func SetupRouter(handler *Handler) *gin.Engine {
	r := gin.Default()

	// 配置CORS
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"*"},
		AllowCredentials: true,
	}))

	// 静态文件服务
	r.Static("/static", "./static")
	r.LoadHTMLGlob("*.html")

	// 首页路由
	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", nil)
	})

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"message": "AI Search Service is running",
		})
	})

	// API路由组
	api := r.Group("/api")
	{
		api.POST("/write", handler.WriteDataset)
		api.POST("/delete", handler.DeleteDataset)
		api.POST("/search", handler.SearchDataset)
		api.POST("/chat_search", handler.ChatSearchDataset)
		api.POST("/query_completion", handler.QueryCompletion)
	}

	return r
}
