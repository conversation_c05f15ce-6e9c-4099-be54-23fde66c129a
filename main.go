package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

type Config struct {
	AccessKey     string `json:"access_key"`
	Secret<PERSON>ey     string `json:"secret_key"`
	DatasetID     string `json:"dataset_id"`
	ApplicationID string `json:"application_id"`
	BaseURL       string `json:"base_url"`
}

type WriteRequest struct {
	Documents []Document `json:"documents"`
}

type Document struct {
	ID      string                 `json:"id"`
	Content map[string]interface{} `json:"content"`
}

type DeleteRequest struct {
	DocumentIDs []string `json:"document_ids"`
}

type SearchRequest struct {
	Query string `json:"query"`
	Limit int    `json:"limit,omitempty"`
}

// VolcesClient 火山AI客户端接口
type VolcesClient interface {
	WriteDataset(documents []Document) (*http.Response, error)
	DeleteDataset(documentIDs []string) (*http.Response, error)
	SearchDataset(query string, limit int) (*http.Response, error)
}

// // DefaultVolcesClient 默认的火山AI客户端实现
// type DefaultVolcesClient struct {
// 	Config Config
// }

var config Config
var volcesClient VolcesClient

func loadConfig() error {
	file, err := os.Open("config.json")
	if err != nil {
		return err
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	return decoder.Decode(&config)
}

// // 默认的鉴权方法 - 您可以替换这个实现
// func (c *DefaultVolcesClient) generateSignature(method, uri, query, body, timestamp string) string {
// 	stringToSign := method + "\n" + uri + "\n" + query + "\n" + body + "\n" + timestamp
// 	h := hmac.New(sha256.New, []byte(c.Config.SecretKey))
// 	h.Write([]byte(stringToSign))
// 	return hex.EncodeToString(h.Sum(nil))
// }

// // 默认的请求方法 - 您可以替换这个实现
// func (c *DefaultVolcesClient) makeRequest(method, endpoint string, body []byte) (*http.Response, error) {
// 	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
// 	bodyStr := string(body)

// 	signature := c.generateSignature(method, endpoint, "", bodyStr, timestamp)

// 	req, err := http.NewRequest(method, c.Config.BaseURL+endpoint, bytes.NewBuffer(body))
// 	if err != nil {
// 		return nil, err
// 	}

// 	req.Header.Set("Content-Type", "application/json")
// 	req.Header.Set("X-Volces-Date", timestamp)
// 	req.Header.Set("Authorization", fmt.Sprintf("HMAC-SHA256 Credential=%s, Signature=%s", c.Config.AccessKey, signature))

// 	client := &http.Client{Timeout: 30 * time.Second}
// 	return client.Do(req)
// }

// // 实现VolcesClient接口的方法
// func (c *DefaultVolcesClient) WriteDataset(documents []Document) (*http.Response, error) {
// 	body, _ := json.Marshal(map[string]interface{}{"documents": documents})
// 	endpoint := fmt.Sprintf("/dataset/%s/write", c.Config.DatasetID)
// 	return c.makeRequest("POST", endpoint, body)
// }

// func (c *DefaultVolcesClient) DeleteDataset(documentIDs []string) (*http.Response, error) {
// 	body, _ := json.Marshal(documentIDs)
// 	endpoint := fmt.Sprintf("/dataset/%s/delete", c.Config.DatasetID)
// 	return c.makeRequest("POST", endpoint, body)
// }

// func (c *DefaultVolcesClient) SearchDataset(query string, limit int) (*http.Response, error) {
// 	body, _ := json.Marshal(map[string]interface{}{"query": query, "limit": limit})
// 	endpoint := fmt.Sprintf("/application/%s/search", c.Config.ApplicationID)
// 	return c.makeRequest("POST", endpoint, body)
// }

// HTTP处理函数
func writeDataset(c *gin.Context) {
	var req WriteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := volcesClient.WriteDataset(req.Documents)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	var result map[string]interface{}
	json.Unmarshal(respBody, &result)

	c.JSON(resp.StatusCode, result)
}

func deleteDataset(c *gin.Context) {
	var req DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := volcesClient.DeleteDataset(req.DocumentIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	var result map[string]interface{}
	json.Unmarshal(respBody, &result)

	c.JSON(resp.StatusCode, result)
}

func searchDataset(c *gin.Context) {
	var req SearchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := volcesClient.SearchDataset(req.Query, req.Limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	var result map[string]interface{}
	json.Unmarshal(respBody, &result)

	c.JSON(resp.StatusCode, result)
}

func main() {
	if err := loadConfig(); err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 初始化火山AI客户端 - 您可以在这里替换为自定义实现
	volcesClient = &DefaultVolcesClient{Config: config}

	r := gin.Default()

	// 配置CORS
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"*"},
		AllowCredentials: true,
	}))

	// 静态文件服务
	r.Static("/static", "./static")
	r.LoadHTMLGlob("*.html")

	// 路由
	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", nil)
	})

	api := r.Group("/api")
	{
		api.POST("/write", writeDataset)
		api.POST("/delete", deleteDataset)
		api.POST("/search", searchDataset)
	}

	fmt.Println("服务器启动在 http://localhost:8080")
	r.Run(":8080")
}
